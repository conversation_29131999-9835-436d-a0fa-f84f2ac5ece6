import request from '@/common/request'
import config from '@/config'
import { getPtToken } from '@/utils/auth'
// 查询组织树
export function getTreeDatas(query) {
    return request.get(config.zhBase<PERSON><PERSON> + '/prod-api/system/user/deptTree', query, {token:'Bearer ' + getPtToken()})
}

export function getTableDatas(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/enterpriseBasicInfoe/list', query, {token:'Bearer ' + getPtToken()})
}

export function getGroupBasicInfoList(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/groupBasicInfo/list', query, {token:'Bearer ' + getPtToken()})
}

export function getEnterpriseRepresentativeList(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/enterpriseRepresentative/list', query, {token:'Bearer ' + getPtToken()})
}


export function getEnterpriseBasicInfoe(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/enterpriseBasicInfoe/' + query.id, {}, {token:'Bearer ' + getPtToken()})
}
export function getGroupBasicInfoe(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/groupBasicInfo/' + query.id, {}, {token:'Bearer ' + getPtToken()})
}
export function getEnterpriseRepresentative(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/enterpriseRepresentative/' + query.id, {}, {token:'Bearer ' + getPtToken()})
}





