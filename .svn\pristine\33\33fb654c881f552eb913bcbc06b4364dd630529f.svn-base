<template>
  <view class="attendance-info">
    <view v-for="(item, index) in items" :key="index" class="info-card">
      <view class="info-header">
        <text class="info-label">{{ item.label }}</text>
        <text v-if="item.badge" class="info-badge">{{ item.badge }}</text>
      </view>
      <view class="info-content">
        <template v-if="Array.isArray(item.content)">
          <view v-for="(line, lineIndex) in item.content" 
                :key="lineIndex" 
                class="content-line">
            {{ line }}
          </view>
        </template>
        <template v-else>
          <text>{{ item.content }}</text>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AttendanceInfo',
  props: {
    items: {
      type: Array,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.attendance-info {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .info-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
    
    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .info-label {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
      
      .info-badge {
        font-size: 12px;
        color: #0484D0;
        background: rgba(4, 132, 208, 0.1);
        padding: 2px 8px;
        border-radius: 10px;
      }
    }
    
    .info-content {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      
      .content-line {
        position: relative;
        padding-left: 12px;
        margin-bottom: 8px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 8px;
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: #0484D0;
        }
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>