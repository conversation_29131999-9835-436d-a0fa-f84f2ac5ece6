import request from '@/common/request'
import config from '@/config'
import { getPtToken } from '@/utils/auth'
// 查询组织树
export function getTermDatas(query) {
    return request.get(config.zhBaseApi + '/prod-api/gslzw/jie/list/order', query, {token:'Bearer ' + getPtToken()})
}
export function getTreeDatas(query) {
    return request.get(config.zhBaseApi + '/prod-api/system/user/deptTree', query, {token:'Bearer ' + getPtToken()})
}

export function getCongressInfoList(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/basicInfo/list', query, {token:'Bearer ' + getPtToken()})
}

export function getDetailDatas(query) {
    return request.get(config.zhBaseApi + '/prod-api/management/basicInfo/detail/' + query.id, {}, {token:'Bearer ' + getPtToken()})
}
export function getZcwrylxData(query) {
    return request.get(config.zhBaseApi + '/prod-api/gslzw/info/executiveType/list', query, {token:'Bearer ' + getPtToken()})
}
export function getXzjbData(query) {
    return request.get(config.zhBaseApi + '/prod-api/gslzw/info/exeLevel/list', query, {token:'Bearer ' + getPtToken()})
}




