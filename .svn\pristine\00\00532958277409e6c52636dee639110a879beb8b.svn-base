import request from '@/common/request'
import config from '@/config'
import { getMhToken } from '@/utils/auth'
// 商会会员
export function members(query) {
    return request.get(config.baseApi1 + '/dev-api/portal/pc/members', query, {token:getMhToken()})
}
// 查询用户
export function getUserInfo(query) {
    return request.get(config.baseApi1 + '/api/api/layout_03_01/login/getUserInfo', query, {token:getMhToken()})
}
// 查询用户
export function deptTree(query) {
    return request.get(config.baseApi1 + '/dev-api/system/user/nologin/deptTree', query, {token:getMhToken()})
}
// 下拉类型查询
export function dictlist(query) {
    return request.get(config.baseApi1 + '/dev-api/system/dict/data/list', query, {token:getMhToken()})
}
//添加企业
export function enterpriseadd(query) {
    return request.post(config.baseApi1 + '/dev-api/commerceIndustryMemberRegister/enterprise/add', query, {token:getMhToken()})
}
//添加企业
export function userApplyadd(query) {
    return request.post(config.baseApi1 + '/api/api/layout_03_01/userApply/add', query, {token:getMhToken()})
}
//企业修改
export function enterpriseebedit(query) {
    return request.post(config.baseApi1 + '/dev-api/hyk/enterprise/eb/edit', query, {token:getMhToken()})
}
//企业信息查询
export function enterprisecx(query) {
    return request.get(config.baseApi1 + '/dev-api/hyk/enterprise/'+query.id, {}, {token:getMhToken()})
}
/////////商会
//添加企业商会
export function enterpriseBasicInfoadd(query) {
    return request.post(config.baseApi1 + '/dev-api/commerceMemberRegister/enterpriseBasicInfo/add',query, {token:getMhToken()})
}
//修改企业商会
export function enterpriseBasicInfoeedit(query) {
    return request.post(config.baseApi1 + '/dev-api/management/enterpriseBasicInfoe/edit',query, {token:getMhToken()})
}
//个人信息
//人员查询
export function sgpersons(query) {
    return request.get(config.baseApi1 + '/dev-api/portal/pc/sg/persons',query, {token:getMhToken()})
}
//人员查询
export function hykpersonal(query) {
    return request.get(config.baseApi1 + '/dev-api/hyk/personal/'+query.id, {}, {token:getMhToken()})
}
//人员添加
export function grpersonaladd(query) {
    return request.post(config.baseApi1 + '/dev-api/commerceIndustryMemberRegister/personal/add',query, {token:getMhToken()})
}
//人员修改
export function personaledit(query) {
    return request.post(config.baseApi1 + '/dev-api/hyk/personal/edit',query, {token:getMhToken()})
}
////商会
//人员添加
export function enterpriseRepresentativeadd(query) {
    return request.post(config.baseApi1 + '/dev-api/commerceMemberRegister/enterpriseRepresentative/add',query, {token:getMhToken()})
}
//人员修改
export function enterpriseRepresentativeperadd(query) {
    return request.post(config.baseApi1 + '/dev-api/management/enterpriseRepresentative/per/add',query, {token:getMhToken()})
}
//////团体
//工商联
////团体工商联添加
export function groupadd(query) {
    return request.post(config.baseApi1 + '/dev-api/commerceIndustryMemberRegister/group/add',query, {token:getMhToken()})
}
////团体工商联信息查询
export function groupcx(query) {
    return request.get(config.baseApi1 + '/dev-api/hyk/group/'+query.id, {}, {token:getMhToken()})
}
////团体工商联修改
export function groupedit(query) {
    return request.post(config.baseApi1 + '/dev-api/hyk/group/gb/edit',query, {token:getMhToken()})
}
////团体商会添加
export function groupBasicInfoadd(query) {
    return request.post(config.baseApi1 + '/dev-api/commerceMemberRegister/groupBasicInfo/add',query, {token:getMhToken()})
}
////团体商会信息查询
export function managementcx(query) {
    return request.get(config.baseApi1 + '/dev-api/management/groupBasicInfo/'+query.id, {}, {token:getMhToken()})
}
////团体商会修改
export function managementxg(query) {
    return request.post(config.baseApi1 + '/dev-api/management/groupBasicInfo/edit',query, {token:getMhToken()})
}




