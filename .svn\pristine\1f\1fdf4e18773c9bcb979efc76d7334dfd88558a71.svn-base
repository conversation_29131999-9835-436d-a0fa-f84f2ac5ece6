/**
 * Utility functions for handling streaming responses
 */

/**
 * Process a streaming response from the API
 * @param {string} url - The API endpoint URL
 * @param {FormData} formData - The form data to send
 * @param {Function} onChunk - Callback function for each chunk of data received
 * @param {Function} onComplete - Callback function when the stream is complete
 * @param {Function} onError - Callback function when an error occurs
 */
export function fetchEventStream(url, formData, onChunk, onComplete, onError) {
  // Create a fetch request with the appropriate headers
  fetch(url, {
    method: 'POST',
    body: formData,
    // No need to set Content-Type header as it will be automatically set with the boundary
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Get a reader from the response body stream
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // Function to process the stream
      function processStream() {
        return reader.read().then(({ done, value }) => {
          if (done) {
            // Process any remaining data in the buffer
            if (buffer.trim()) {
              onChunk(buffer.trim());
            }
            onComplete();
            return;
          }

          // Decode the chunk and add it to our buffer
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Process complete events in the buffer
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep the last incomplete line in the buffer

          for (const line of lines) {
            if (line.startsWith('data:')) {
              const data = line.substring(5).trim();
              if (data) {
                onChunk(data);
              }
            }
          }

          // Continue reading
          return processStream();
        });
      }

      return processStream();
    })
    .catch(error => {
      onError(error);
    });
}
